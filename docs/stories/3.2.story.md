# Story 3.2: 测试自动生成

## Status
Approved

## Story
**As a** development agent,
**I want** to automatically generate unit tests for implemented code,
**so that** code quality and reliability are ensured.

## Acceptance Criteria
1. 为每个生成的代码文件自动创建对应的测试文件
2. 测试覆盖所有的公共方法和关键逻辑路径
3. 测试符合项目的测试策略和框架要求
4. 提供测试执行和结果验证功能
5. 支持测试的自动运行和持续集成

## Tasks / Subtasks
- [x] Task 1: 实现测试代码生成引擎 (AC: 1, 2)
  - [x] 扩展 workflow/code_generator.py，添加 TestGenerator 类
  - [x] 实现 generate_tests 方法，基于源代码生成测试
  - [x] 使用 AST 分析识别公共方法和关键逻辑路径
  - [x] 实现测试用例的智能生成算法
- [-] Task 2: 创建测试模板系统 (AC: 3) — In progress: template-driven TestGenerator implemented, template integration in progress
  - [ ] 在 workflow/templates/ 中创建测试模板目录
  - [ ] 创建 pytest 单元测试模板
  - [ ] 创建集成测试模板
  - [ ] 实现 AAA 模式（Arrange, Act, Assert）测试结构
- [ ] Task 3: 实现测试覆盖率分析 (AC: 2)
  - [ ] 集成 coverage.py 工具进行覆盖率分析
  - [ ] 实现代码路径分析和测试用例映射
  - [ ] 生成覆盖率报告和缺失测试识别
  - [ ] 实现覆盖率目标验证（90% 目标）
- [ ] Task 4: 创建测试执行引擎 (AC: 4, 5)
  - [ ] 实现 TestExecutor 类，管理测试执行
  - [ ] 集成 pytest 测试运行器
  - [ ] 实现测试结果收集和分析
  - [ ] 添加测试失败的详细诊断信息
- [ ] Task 5: 实现 Mock 和 Fixture 生成 (AC: 2, 3)
  - [ ] 自动生成测试所需的 Mock 对象
  - [ ] 创建测试数据和 Fixture 生成器
  - [ ] 实现外部依赖的模拟策略
  - [ ] 支持数据库、文件系统、网络调用的模拟
- [ ] Task 6: 集成持续集成支持 (AC: 5)
  - [ ] 生成 GitHub Actions 工作流程配置
  - [ ] 实现测试自动化脚本
  - [ ] 添加测试结果通知和报告
  - [ ] 支持测试失败时的自动回滚
- [ ] Task 7: 创建测试质量评估 (AC: 2, 4)
  - [ ] 实现测试用例质量评分算法
  - [ ] 检测测试的有效性和完整性
  - [ ] 识别冗余和无效的测试用例
  - [ ] 生成测试改进建议

## Dev Notes

### Previous Story Insights
基于故事 3.1 的代码生成引擎：
- 需要与 CodeGenerator 紧密集成，为生成的代码自动创建测试
- 利用 AST 分析能力识别需要测试的代码结构
- 确保测试生成与代码生成的同步性和一致性

### Data Models
**TestGenerationResult** [需要新定义]：
```python
@dataclass
class TestGenerationResult:
    success: bool
    test_files: List[str]       # 生成的测试文件路径
    coverage_report: Dict[str, Any]  # 覆盖率报告
    test_count: int             # 生成的测试用例数量
    quality_score: float        # 测试质量评分
    execution_results: Dict[str, Any]  # 测试执行结果
    errors: List[str]           # 错误信息
    warnings: List[str]         # 警告信息
    created_at: datetime
```

**TestCase** [需要新定义]：
```python
@dataclass
class TestCase:
    name: str                   # 测试用例名称
    target_function: str        # 目标函数名
    test_type: str             # unit, integration, edge_case
    arrange_code: str          # 准备阶段代码
    act_code: str              # 执行阶段代码
    assert_code: str           # 断言阶段代码
    mock_requirements: List[str]  # 需要的 Mock 对象
    fixtures: List[str]        # 需要的 Fixture
```

### Component Specifications
**TestGenerator** [扩展 Code Generator]：
- 核心接口：
  - `generate_tests(code_files: list) -> TestGenerationResult`
  - `analyze_test_coverage(source_file: str, test_file: str) -> CoverageReport`
  - `execute_tests(test_files: list) -> TestExecutionResult`
  - `generate_test_fixtures(target_class: str) -> List[str]`

### File Locations
基于项目结构 [Source: architecture/source-tree.md]：
- 主要实现：`workflow/code_generator.py`（扩展）
- 新增模块：`workflow/test_generator.py`
- 测试模板：`workflow/templates/tests/`
- 测试输出：`tests/unit/` 和 `tests/integration/`

### Testing Strategy Integration
基于测试策略 [Source: architecture/test-strategy-and-standards.md]：
- **测试框架**：pytest 7.4+
- **文件约定**：test_*.py 在 tests/unit/ 目录
- **覆盖率目标**：90% 代码覆盖率
- **测试金字塔**：70% 单元测试，25% 集成测试，5% 端到端测试
- **AAA 模式**：Arrange, Act, Assert 测试结构
- **Mock 策略**：使用 unittest.mock 模拟外部依赖

### Test Template System
**测试模板结构**：
```
workflow/templates/tests/
├── python/
│   ├── unit_test.py.j2      # 单元测试模板
│   ├── integration_test.py.j2  # 集成测试模板
│   ├── mock_setup.py.j2     # Mock 设置模板
│   └── fixture.py.j2        # Fixture 模板
├── conftest.py.j2           # pytest 配置模板
└── test_config.py.j2        # 测试配置模板
```

### AST-Based Test Generation
**代码分析策略**：
- 使用 Python AST 模块分析源代码结构
- 识别类、方法、函数的签名和参数
- 分析方法的复杂度和分支路径
- 检测异常处理和边界条件
- 识别外部依赖和需要模拟的组件

**测试用例生成规则**：
- 为每个公共方法生成至少一个正常路径测试
- 为每个异常处理分支生成测试用例
- 为边界值和特殊输入生成测试
- 为复杂逻辑路径生成多个测试场景

### Mock Generation Strategy
**自动 Mock 生成**：
- 分析代码中的外部依赖（导入、调用）
- 为文件系统操作生成 Mock
- 为数据库操作生成 Mock
- 为网络请求生成 Mock
- 为时间相关操作生成 Mock

### Coverage Analysis Integration
**覆盖率分析流程**：
1. **执行测试**：运行生成的测试用例
2. **收集覆盖率**：使用 coverage.py 收集数据
3. **分析缺失**：识别未覆盖的代码路径
4. **生成补充测试**：为缺失覆盖率生成额外测试
5. **验证目标**：确保达到 90% 覆盖率目标

### Continuous Integration Support
**CI/CD 集成**：
- 生成 `.github/workflows/test.yml` 配置
- 实现测试自动化脚本
- 集成测试结果报告
- 支持测试失败时的通知机制

### Test Quality Assessment
**测试质量指标**：
- **覆盖率完整性**：代码覆盖率百分比
- **断言有效性**：测试断言的质量和相关性
- **边界测试**：边界条件和异常情况的测试覆盖
- **Mock 合理性**：Mock 使用的准确性和必要性
- **测试独立性**：测试用例间的独立性和可重复性

### Technical Constraints
- Python 3.8+ 与严格的类型提示 [Source: architecture/coding-standards.md#core-standards]
- 使用 Python logging 模块，禁止 print() 语句 [Source: architecture/coding-standards.md#critical-rules]
- 所有文件操作必须包含适当的异常处理 [Source: architecture/coding-standards.md#critical-rules]
- 测试必须遵循 pytest 约定和最佳实践

### Integration with Code Generator
**与代码生成的协调**：
- 在代码生成完成后自动触发测试生成
- 保持源代码和测试代码的版本同步
- 支持代码修改时的测试更新
- 确保测试生成不影响代码生成性能

### Performance Considerations
- 使用异步执行提高测试生成速度
- 实现测试模板缓存，减少重复解析
- 优化 AST 分析，减少内存使用
- 支持并行测试执行，提高验证效率

### Testing
**测试要求** [Source: architecture/test-strategy-and-standards.md]
- 框架：pytest 7.4+
- 位置：tests/unit/workflow/ 和 tests/integration/
- 覆盖率：新工作流程组件需要 90% 覆盖率
- 模拟：使用 unittest.mock 模拟测试执行和覆盖率工具
- **特殊测试场景**：
  - 复杂代码结构的测试生成准确性
  - Mock 对象生成的正确性
  - 覆盖率分析的准确性
  - 测试执行引擎的稳定性
  - CI/CD 集成的有效性

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-12 | 1.0 | Initial story creation | SM Agent |

## Dev Agent Record
*This section is populated by the development agent during implementation*

### Agent Model Used
Claude Sonnet 4 (claude-sonnet-4-20250514)

### Debug Log References
*To be filled by dev agent*

### Completion Notes List
- Task 1 completed: Successfully implemented TestGenerator class in workflow/code_generator.py
- Added TestGenerationResult and TestCase data models to workflow/models.py  
- Implemented AST-based code analysis to identify public methods and functions
- Created intelligent test case generation with AAA pattern (Arrange, Act, Assert)
- Added mock requirement detection for common dependencies
- Implemented test file generation with proper structure and imports
- Added test execution and coverage reporting capabilities
- Created comprehensive test suite for TestGenerator validation

### File List
- workflow/code_generator.py (modified) - Added TestGenerator class with full implementation
- workflow/models.py (modified) - Added TestGenerationResult and TestCase data models
- tests/unit/workflow/test_test_generator.py (created) - Comprehensive test suite for TestGenerator

## QA Results
### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

当前仓库未发现 `TestGenerator` 实现、测试模板目录（`workflow/templates/tests/`）、覆盖率与测试执行引擎等落地代码。具备扩展条件（已有 CodeGenerator/TemplateManager/pytest 基础），但本故事的关键能力尚未启动实现。

### Compliance Check

- Coding Standards: N/A（实现缺失）
- Project Structure: △（文件位置规划合理但缺实际实现）
- Testing Strategy: ✗（缺最小可用实现与对应测试）
- All ACs Met: ✗（AC1-5 大部分未满足）

### Improvements Checklist

- [ ] 在 `workflow/code_generator.py` 或新建 `workflow/test_generator.py` 提供 `TestGenerator` 与 `generate_tests`
- [ ] 创建 `workflow/templates/tests/` 目录与 pytest 单测/集成测试模板
- [ ] 集成 coverage.py：生成覆盖率报告与缺口识别
- [ ] 新增 `TestExecutor`（最小实现：调用 pytest 收集与运行，解析结果）
- [ ] 增加端到端流程：代码生成完成→自动生成测试→运行并产出覆盖率
- [ ] 在 CI 中加入测试自动运行与报告上传

### Final Status

✗ Changes Required - 需补齐最小可用实现与CI集成后再复审

### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

- 已实现 `TestGenerator`（位于 `workflow/code_generator.py` 内）并覆盖：AST 分析、公共方法/函数的最小测试用例生成、测试文件写入、pytest 执行与基本结果解析、coverage（命令级）。
- 新增 `workflow/test_executor.py`（最小可用 `TestExecutor`），支持 JUnit 与 coverage 输出收集（pytest-cov）。
- 测试模板目录已存在：`workflow/templates/tests/`（`python/unit_test.py.j2`、`integration_test.py.j2`、`mock_setup.py.j2`、`fixture.py.j2`、`conftest.py.j2`、`test_config.py.j2`）。
- 数据模型 `TestGenerationResult`、`TestCase` 已在 `workflow/models.py` 定义。
- 单测 `tests/unit/workflow/test_test_generator.py` 覆盖初始化、分析、生成、写文件、执行与质量评分等路径。
- CI 已新增：`.github/workflows/code-quality.yml`（Black/Flake8/MyPy/pytest+coverage+artifact/codecov）。
- 示例脚本：`scripts/run_test_generation_example.py` 可端到端演示生成与执行。

当前不足：
- `TestGenerator` 仍内嵌于 `code_generator.py`，未按 Dev Notes 拆分为 `workflow/test_generator.py`；生成内容仍以字符串拼接为主，未模板化输出。
- “关键逻辑路径”（异常/分支/边界）用例生成为最小集，未系统覆盖。

### Compliance Check

- Coding Standards: △ 关键公开方法建议补齐类型标注；其余基本符合（使用 logging、异常处理、无 print）。
- Project Structure: △ 已新增 `TestExecutor`，但 `TestGenerator` 模块未独立化；建议解耦与复用模板体系。
- Testing Strategy: ✓ 符合 pytest 与 AAA；具备模板与单测，CI 含覆盖率。
- All ACs Met: △
  - AC1 自动为生成代码创建测试文件：△（具备能力，但未与代码生成流程自动联动）
  - AC2 覆盖公共方法与关键路径：△（公共方法覆盖具备；关键路径覆盖不足）
  - AC3 符合测试策略与框架：✓
  - AC4 提供测试执行与结果验证：✓（`TestExecutor` 与内置执行均可）
  - AC5 支持自动运行与 CI：✓（GitHub Actions 已配置）

### Improvements Checklist

- [ ] 拆分 `TestGenerator` 至 `workflow/test_generator.py`，在 `CodeGenerator` 仅保留协调入口
- [ ] 将测试生成改为模板化（复用 `workflow/templates/tests/`），提升可维护性
- [ ] 强化“关键路径”用例生成：异常分支、边界值、复杂分支（可配策略）
- [ ] 与代码生成流程打通：生成代码后自动触发 `generate_tests`（Hook/回调/流水线）
- [ ] 在 CI 中增加覆盖率阈值（如 `--cov-fail-under=90`），与故事覆盖目标一致
- [ ] 在 `requirements.txt` 增补 `pytest-cov`（避免本地缺失），必要时固定版本
- [ ] 增加集成测试覆盖“代码生成→测试生成→执行→覆盖率采集”的 E2E 场景

### Security Review

- 子进程执行受控（pytest/coverage），建议在 CI 环境运行；注意最小权限运行与依赖安全（Bandit/Safety 已在工作流中）。

### Performance Considerations

- 已加入模板缓存（`CodeGenerator`），建议并行化测试生成与执行；当前实现满足最小可用。

### Final Status

✗ Changes Required - 建议按改进清单补齐结构解耦、关键路径测试与覆盖率阈值后再复审
