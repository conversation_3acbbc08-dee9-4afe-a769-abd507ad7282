from __future__ import annotations
import logging
from pathlib import Path
from typing import Dict, Any, Optional

from workflow import state_engine
from workflow.document_processor import shard_documents
from workflow.models import WorkflowExecution
from workflow.error_handler import ErrorClassifier, generate_recovery_suggestions

logger = logging.getLogger(__name__)


def _create_minimal_epics_and_stories(project_path: str, shard_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    从分片结果生成最小化的 Epics 与 Stories 文件，写入 docs/epics/ 和 docs/stories/。
    该实现为 MVP：
    - 为每个分片创建一个 Epic 文件（基于分片文件名）
    - 在 docs/stories/ 中为每个 Epic 创建一条最小 Story（引用 Epic）
    返回生成结果的摘要字典，包含 paths 列表和 any errors。
    """
    from pathlib import Path
    from datetime import datetime

    results = {"epics": [], "stories": [], "errors": []}
    try:
        epics_dir = Path(project_path) / "docs" / "epics"
        stories_dir = Path(project_path) / "docs" / "stories"
        epics_dir.mkdir(parents=True, exist_ok=True)
        stories_dir.mkdir(parents=True, exist_ok=True)

        # shard_result expected to have "sharded_files" list of paths (relative or absolute)
        sharded_files = shard_result.get("sharded_files") or []
        timestamp = datetime.utcnow().isoformat() + "Z"

        for idx, shard_path in enumerate(sharded_files):
            # Normalize names
            shard_name = Path(shard_path).stem if shard_path else f"shard_{idx}"
            epic_name = f"epic-{shard_name}"
            epic_filename = epics_dir / f"{epic_name}.md"
            story_name = f"{epic_name}-story-1"
            story_filename = stories_dir / f"{story_name}.story.md"

            # Minimal Epic content
            epic_content = f"# Epic: {epic_name}\n\n" \
                           f"Created from PRD shard: {shard_path}\n\n" \
                           f"- created_at: {timestamp}\n" \
                           f"- source_shard: {shard_path}\n"

            # Minimal Story content (markdown with front-matter style fields)
            story_content = f"# Story: {story_name}\n\n" \
                            f"## From Epic\n" \
                            f"- epic: {epic_name}\n\n" \
                            f"## Summary\n" \
                            f"自动生成的最小 Story，用于将 shard `{shard_path}` 转为开发任务。\n\n" \
                            f"## Acceptance Criteria\n" \
                            f"1. 已校验分片内容\n" \
                            f"2. 生成基本 Epic 与 Story 文件\n\n" \
                            f"## created_at: {timestamp}\n"

            try:
                epic_filename.write_text(epic_content, encoding="utf-8")
                story_filename.write_text(story_content, encoding="utf-8")
                results["epics"].append(str(epic_filename))
                results["stories"].append(str(story_filename))
            except Exception as e:
                results["errors"].append({"shard": shard_path, "error": str(e)})
    except Exception as e:
        results["errors"].append({"phase": "create_epics_stories", "error": str(e)})
    return results


def _run_document_sharding(project_path: str, doc_filename: str = "docs/prd.md") -> Dict[str, Any]:
    """
    Wrapper that runs document sharding using workflow.document_processor.shard_documents
    and normalizes the result into a simple dict for the executor.

    This wrapper integrates with the RetryManager so transient errors can be retried
    with exponential backoff. It also logs structured error records via the
    workflow.error_handler utilities.
    """
    from workflow.error_handler import RetryManager, ErrorClassifier
    from workflow.models import RetryPolicy

    src_path = Path(project_path) / doc_filename
    logger.info("Running document sharding for %s", src_path)

    retry_policy = RetryPolicy(max_retries=3, base_delay=1.0, max_delay=30.0, backoff_multiplier=2.0, retry_conditions=["temporary"])
    retry_mgr = RetryManager(policy=retry_policy)

    def _shard() -> Any:
        # call the underlying shard_documents function
        return shard_documents(str(src_path))

    try:
        # use the retry manager to execute sharding (handles sync and async transparently)
        result = retry_mgr.execute_with_retry(_shard, context={"operation": "shard_documents"}, phase="document_processing", component="document_processor")
        if not result.success:
            logger.warning("Sharding reported failure: %s", getattr(result, "errors", None))
            return {"success": False, "errors": getattr(result, "errors", []), "sharded_files": getattr(result, "sharded_files", []), "index_file": getattr(result, "index_file", None)}
        return {"success": True, "sharded_files": result.sharded_files, "index_file": result.index_file}
    except Exception as e:
        # classify the final exception for structured logging and diagnostics
        err_rec = ErrorClassifier.classify_error(e, context={"path": str(src_path)}, phase="document_processing", component="document_processor")
        logger.exception("Exception during sharding after retries: %s | error_id=%s", e, err_rec.error_id, extra={"error_record": err_rec.to_dict()})
        return {"success": False, "errors": [str(e)], "sharded_files": [], "index_file": None}


def start_workflow_executor(project_path: str, auto_confirm: bool = False, doc_filename: str = "docs/prd.md", interaction_callback: Optional[callable] = None) -> Dict[str, Any]:
    """
    Lightweight workflow executor entrypoint (MVP).

    Steps (MVP):
    1. Scan project structure.
    2. Produce initial WorkflowExecution record (in-memory).
    3. Optionally run document sharding for PRD if detected/missing shards.
    4. Return an execution summary including scan, workflow_execution, and any sharding result.

    This implementation is synchronous and persists basic execution state to .workflow_state for checkpointing.
    """
    logger.info("Starting workflow executor for project: %s (auto_confirm=%s)", project_path, auto_confirm)

    scan = state_engine.scan_project_structure(project_path)
    detected = state_engine.detect_current_stage(scan)

    # Create a minimal WorkflowExecution record (uses model shape; will persist)
    exec_id = f"exec-{int(__import__('time').time())}"
    wf = WorkflowExecution(
        execution_id=exec_id,
        workflow_type="greenfield",
        current_phase=detected,
        total_phases=4,
        completed_phases=0,
    )

    # serialize and persist initial execution state for checkpointing and later resume
    wf_payload = wf.to_dict() if hasattr(wf, "to_dict") else wf.__dict__
    # attach minimal metadata expected by state_engine persistence utilities
    workflow_state_payload = {
        "workflow_id": exec_id,
        "workflow_execution": wf_payload,
        "current_phase": detected,
        "project_path": project_path,
        "status": "running",
        "created_at": wf_payload.get("started_at") or __import__("datetime").datetime.utcnow().isoformat() + "Z",
        "updated_at": wf_payload.get("updated_at") or __import__("datetime").datetime.utcnow().isoformat() + "Z",
        "phase_progress": {},
        "user_interactions": [],
    }

    try:
        saved_state_path = state_engine.save_execution_state(project_path, workflow_state_payload)
    except Exception as e:
        logger.exception("Failed to persist initial workflow state: %s", e)
        saved_state_path = None

    summary: Dict[str, Any] = {
        "scan": scan,
        "workflow_execution": wf_payload,
        "saved_state_path": saved_state_path
    }

    # If detected indicates document_processing or prd shards are missing, attempt sharding
    try_shard = detected in ("document_processing", "document_validated") or not scan.get("prd_shards")
    if try_shard:
        # 在执行关键步骤前保存检查点
        try:
            checkpoint_name = "before_shard"
            cp_payload = {"workflow_state": {**workflow_state_payload, "current_phase": wf.current_phase}}
            state_engine.save_checkpoint(project_path, wf.execution_id, checkpoint_name, cp_payload)
        except Exception as e:
            logger.warning("Failed to save checkpoint before sharding: %s", e)

        shard_result = _run_document_sharding(project_path, doc_filename=doc_filename)
        summary["shard_result"] = shard_result
        if shard_result.get("success"):
            # update workflow execution phase progress minimally
            wf.phase_progress["document_processing"] = 1.0
            wf.completed_phases = min(wf.completed_phases + 1, wf.total_phases)
            wf.current_phase = "epic_creation"
            summary["workflow_execution"] = wf.to_dict()

            # 关键步骤后保存检查点
            try:
                checkpoint_name = "after_shard"
                cp_payload = {"workflow_state": {**workflow_state_payload, "current_phase": wf.current_phase, "phase_progress": wf.phase_progress}}
                state_engine.save_checkpoint(project_path, wf.execution_id, checkpoint_name, cp_payload)
            except Exception as e:
                logger.warning("Failed to save checkpoint after sharding: %s", e)

            # Generate minimal Epics and Stories from shards (MVP)
            try:
                gen_result = _create_minimal_epics_and_stories(project_path, shard_result)
                summary["generated_epics_stories"] = gen_result
                logger.info("Generated %d epics and %d stories", len(gen_result.get("epics", [])), len(gen_result.get("stories", [])))
            except Exception as e:
                logger.exception("Failed to generate epics/stories: %s", e)
                summary.setdefault("generated_epics_stories", {"epics": [], "stories": [], "errors": [str(e)]})
        else:
            # 失败：分类错误并附加恢复建议与最近检查点提示
            try:
                exc_text = ", ".join(shard_result.get("errors") or ["unknown error"])
                err_rec = ErrorClassifier.classify_error(Exception(exc_text), context={"path": str(Path(project_path) / doc_filename)}, phase="document_processing", component="document_processor")
                err_rec.recovery_suggestions = generate_recovery_suggestions(err_rec)
                summary["shard_error"] = err_rec.to_dict()
                # 提供最近检查点名称（after_shard 若存在，否则 before_shard）
                summary["available_checkpoints"] = state_engine.list_checkpoints(project_path, wf.execution_id)
            except Exception as e:
                logger.warning("Failed to classify or list checkpoints: %s", e)
            logger.info("Sharding failed or produced no shards; leaving workflow in document_processing state")
    else:
        logger.info("Skipping sharding; PRD shards already present or not applicable")

    # If auto_confirm is True, perform a light transition
    if auto_confirm:
        logger.info("Auto-confirm requested; advancing state if reasonable")
        if wf.current_phase == "epic_creation":
            wf.current_phase = "story_development"
            wf.phase_progress["epic_creation"] = 0.0
        wf.updated_at = __import__("datetime").datetime.utcnow().isoformat() + "Z"
        summary["workflow_execution"] = wf.to_dict()

    # Interaction point before generating stories/advancing phases
    if interaction_callback:
        try:
            from workflow.models import UserInteraction
            ui = UserInteraction(
                interaction_id=f"ui-{int(__import__('time').time())}",
                phase=wf.current_phase,
                message="Ready to generate Epics/Stories from PRD shards. Choose action.",
                options=["continue", "skip", "retry", "pause"],
                user_choice=None,
                auto_proceed=auto_confirm
            )
            # Allow the callback to mutate or return a simple decision
            decision = interaction_callback(ui)
            if isinstance(decision, str):
                decision = decision.lower()
                ui.user_choice = decision
            elif hasattr(decision, "get") and isinstance(decision, dict):
                # support callbacks that return structured result
                ui.user_choice = decision.get("decision") or decision.get("choice")
                decision = (ui.user_choice or "continue").lower()
            else:
                decision = ui.user_choice or "continue"
    
            # Record the interaction into persistent workflow state payload
            try:
                workflow_state_payload.setdefault("user_interactions", [])
                workflow_state_payload["user_interactions"].append(ui.to_dict())
                # persist after recording interaction
                _ = state_engine.save_execution_state(project_path, workflow_state_payload)
                summary.setdefault("saved_state_updates", []).append({"user_interaction_saved": ui.interaction_id})
            except Exception as e:
                logger.exception("Failed to persist user interaction: %s", e)
                summary.setdefault("saved_state_errors", []).append(str(e))
    
            if decision == "skip":
                logger.info("User chose to skip epic/story generation")
                summary["user_interaction"] = {"decision": "skip", "interaction_id": ui.interaction_id}
            elif decision == "pause":
                logger.info("User chose to pause workflow")
                wf.status = "paused"
                workflow_state_payload["status"] = "paused"
                workflow_state_payload["updated_at"] = __import__("datetime").datetime.utcnow().isoformat() + "Z"
                state_engine.save_execution_state(project_path, workflow_state_payload)
                summary["workflow_execution"] = wf.to_dict()
                summary["user_interaction"] = {"decision": "pause", "interaction_id": ui.interaction_id}
                return summary
            elif decision == "retry":
                logger.info("User chose to retry previous step (will re-run sharding once)")
                shard_result = _run_document_sharding(project_path, doc_filename=doc_filename)
                summary["shard_result_retry"] = shard_result
                if not shard_result.get("success"):
                    logger.info("Retry sharding failed; returning with failure")
                    summary["workflow_execution"] = wf.to_dict()
                    summary["user_interaction"] = {"decision": "retry_failed", "interaction_id": ui.interaction_id}
                    return summary
                gen_result = _create_minimal_epics_and_stories(project_path, shard_result)
                summary["generated_epics_stories_retry"] = gen_result
                # update progress and persist
                wf.phase_progress["document_processing"] = 1.0
                workflow_state_payload["phase_progress"] = wf.phase_progress
                workflow_state_payload["updated_at"] = __import__("datetime").datetime.utcnow().isoformat() + "Z"
                state_engine.save_execution_state(project_path, workflow_state_payload)
                summary["workflow_execution"] = wf.to_dict()
                summary["user_interaction"] = {"decision": "retry_success", "interaction_id": ui.interaction_id}
            else:
                summary["user_interaction"] = {"decision": "continue", "interaction_id": ui.interaction_id}
        except Exception as e:
            logger.exception("Error during interaction callback: %s", e)
            summary["user_interaction_error"] = str(e)

    # Include a human-friendly status report
    try:
        report = state_engine.generate_status_report(summary["workflow_execution"], scan)
        summary["status_report"] = report
    except Exception as e:
        logger.exception("Failed to generate status report: %s", e)
        summary["status_report"] = {"error": str(e)}

    logger.info("Workflow executor finished for %s -> %s", project_path, exec_id)
    return summary


# -- Control helpers: pause / resume / restart (lightweight, use state_engine persistence) --
def pause_workflow_execution(project_path: str, workflow_execution: Dict[str, Any]) -> Dict[str, Any]:
    """
    Pause the given workflow execution by persisting its state and returning metadata.
    """
    try:
        # ensure we have an execution_id/workflow_id shape
        workflow_id = workflow_execution.get("execution_id") or workflow_execution.get("workflow_id") or workflow_execution.get("execution_id")
        if not workflow_id:
            workflow_id = f"wf-{int(__import__('time').time())}"
            workflow_execution["execution_id"] = workflow_id

        # mark paused
        workflow_execution["status"] = "paused"
        workflow_execution["updated_at"] = __import__("datetime").datetime.utcnow().isoformat() + "Z"

        # persist using state_engine
        saved_path = state_engine.save_execution_state(project_path, workflow_execution)
        logger.info("Paused workflow %s, saved state to %s", workflow_id, saved_path)
        return {"paused": True, "workflow_id": workflow_id, "saved_path": saved_path}
    except Exception as e:
        logger.exception("Failed to pause workflow execution: %s", e)
        return {"paused": False, "error": str(e)}


def resume_workflow_execution(project_path: str, workflow_id: str, checkpoint_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Resume a previously paused workflow. If checkpoint_name is provided, restore from checkpoint.
    Returns the restored workflow_state and a status dict.
    """
    try:
        if checkpoint_name:
            restored = state_engine.restore_from_checkpoint(project_path, workflow_id, checkpoint_name)
            logger.info("Restored workflow %s from checkpoint %s", workflow_id, checkpoint_name)
            return {"resumed": True, "workflow_state": restored}
        else:
            # load last saved execution state
            loaded = state_engine.load_execution_state(project_path, workflow_id)
            # update status
            loaded["status"] = "running"
            loaded["updated_at"] = __import__("datetime").datetime.utcnow().isoformat() + "Z"
            # persist updated state
            state_engine.save_execution_state(project_path, loaded)
            logger.info("Resumed workflow %s from saved state", workflow_id)
            return {"resumed": True, "workflow_state": loaded}
    except FileNotFoundError as e:
        logger.warning("Cannot resume workflow %s: %s", workflow_id, e)
        return {"resumed": False, "error": str(e)}
    except Exception as e:
        logger.exception("Failed to resume workflow execution: %s", e)
        return {"resumed": False, "error": str(e)}


def restart_workflow_execution(project_path: str, workflow_execution: Dict[str, Any], force: bool = False) -> Dict[str, Any]:
    """
    Restart a workflow execution: optionally overwrite existing saved state and start fresh.
    """
    try:
        workflow_id = workflow_execution.get("execution_id") or workflow_execution.get("workflow_id")
        if not workflow_id:
            workflow_id = f"wf-{int(__import__('time').time())}"
            workflow_execution["execution_id"] = workflow_id

        workflow_execution["status"] = "running"
        workflow_execution["started_at"] = __import__("datetime").datetime.utcnow().isoformat() + "Z"
        workflow_execution["updated_at"] = workflow_execution["started_at"]

        saved_path = state_engine.save_execution_state(project_path, workflow_execution)
        logger.info("Restarted workflow %s (saved initial state to %s)", workflow_id, saved_path)
        return {"restarted": True, "workflow_id": workflow_id, "saved_path": saved_path}
    except Exception as e:
        logger.exception("Failed to restart workflow execution: %s", e)
        return {"restarted": False, "error": str(e)}


def recover_from_checkpoint(project_path: str, workflow_id: str, checkpoint_name: Optional[str] = None) -> Dict[str, Any]:
    """
    从最近或指定检查点恢复执行状态。
    - 若未提供 checkpoint_name，则选择该 workflow_id 的最后一个检查点文件。
    返回：{restored: bool, workflow_state: dict|None, checkpoint_used: str|None, error?: str}
    """
    try:
        cp_to_use = checkpoint_name
        if not cp_to_use:
            cps = state_engine.list_checkpoints(project_path, workflow_id)
            cp_to_use = cps[-1] if cps else None
            if cp_to_use and cp_to_use.endswith(".checkpoint.json"):
                # 提取名称部分
                cp_to_use = cp_to_use.split("__", 1)[-1].replace(".checkpoint.json", "")
        if not cp_to_use:
            return {"restored": False, "error": "No checkpoint available"}
        restored = state_engine.restore_from_checkpoint(project_path, workflow_id, cp_to_use)
        return {"restored": True, "workflow_state": restored, "checkpoint_used": cp_to_use}
    except FileNotFoundError as e:
        return {"restored": False, "error": str(e)}
    except Exception as e:
        logger.exception("Failed to recover from checkpoint: %s", e)
        return {"restored": False, "error": str(e)}