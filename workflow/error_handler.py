"""
Error handling components for Story 4.2: 错误恢复与重试机制

提供：
- ErrorClassifier: 将异常映射为 ErrorRecord（分类与严重度评估）
- RetryManager: 管理重试策略（指数退避、超时、次数限制）
- execute_with_retry: 将函数包装为自动重试执行（支持同步与异步函数）

注意：
- 使用标准 logging 模块进行结构化日志记录（轻量实现）
- 保持接口简单以便后续集成到 WorkflowExecutor / StateEngine
"""
from __future__ import annotations

import asyncio
import logging
import traceback
import time
from typing import Any, Callable, Dict, Optional

from .models import ErrorRecord, RetryPolicy

logger = logging.getLogger(__name__)


class ErrorClassifier:
    """
    将异常与上下文转换为 ErrorRecord 的辅助类。
    可扩展以支持更复杂的模式识别与历史驱动分类。
    """

    TEMPORARY_ERRORS = ("ConnectionError", "TimeoutError", "BrokenPipeError")
    PERMANENT_ERRORS = ("FileNotFoundError", "PermissionError", "ValueError")

    @classmethod
    def classify_error(cls, exc: Exception, context: Optional[Dict[str, Any]] = None, phase: str = "unknown", component: str = "unknown") -> ErrorRecord:
        """
        基本分类逻辑：
        - 检查异常类型名称匹配临时或永久错误列表
        - 对于未知异常，默认为 system_error 并标记为 high severity
        """
        context = context or {}
        exc_name = type(exc).__name__
        msg = str(exc)
        stack = "".join(traceback.format_exception(type(exc), exc, exc.__traceback__))

        if exc_name in cls.TEMPORARY_ERRORS:
            error_type = "temporary"
            severity = "medium"
        elif exc_name in cls.PERMANENT_ERRORS:
            error_type = "permanent"
            severity = "high"
        else:
            # 默认分类，可根据消息/上下文增强
            error_type = "system_error"
            severity = "high"

        # 将用户取消识别为 user_error（如果上下文表明）
        if "user_cancelled" in context and context.get("user_cancelled"):
            error_type = "user_error"
            severity = "low"

        err = ErrorRecord.new(
            error_type=error_type,
            severity=severity,
            phase=phase,
            component=component,
            error_message=msg,
            context=context,
        )
        err.stack_trace = stack
        return err

    @classmethod
    def evaluate_severity(cls, error_record: ErrorRecord) -> str:
        """
        可以放置更复杂的严重度评估逻辑（例如根据组件、频率、历史记录）。
        当前直接返回已有字段以便扩展。
        """
        return error_record.severity


class RetryManager:
    """
    管理重试执行的策略：
    - 支持指数退避（由 RetryPolicy.compute_delay 提供）
    - 支持总超时、最大重试次数
    - 支持基于 ErrorRecord 的 should_retry 判断
    """

    def __init__(self, policy: Optional[RetryPolicy] = None):
        self.policy = policy or RetryPolicy()

    def should_retry(self, error: ErrorRecord) -> bool:
        """
        判断是否应当重试：
        - 错误类型在重试条件内 (默认 'temporary')
        - 当前重试次数小于 max_retries
        - （可扩展）检查外部条件，如系统负载或熔断状态
        """
        if error.retry_count >= self.policy.max_retries:
            return False
        if error.error_type in self.policy.retry_conditions:
            return True
        return False

    async def execute_with_retry_async(self, func: Callable[..., Any], *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """
        支持异步函数的重试包装。
        timeout 是总重试窗口（秒）。
        """
        start = time.time()
        attempt = 0
        last_exception = None
        while True:
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as exc:
                last_exception = exc
                err = ErrorClassifier.classify_error(exc, context=kwargs.get("context"), phase=kwargs.get("phase", "unknown"), component=kwargs.get("component", "unknown"))
                err.retry_count = attempt
                logger.error("RetryManager caught exception (async): %s | attempt=%d | error_id=%s", str(exc), attempt, err.error_id, extra={"error_record": err.to_dict()})
                if not self.should_retry(err):
                    logger.debug("Not retrying (async): %s", err.to_dict())
                    raise
                # 检查超时
                if timeout is not None and (time.time() - start) > timeout:
                    logger.debug("Retry timeout exceeded (async)")
                    raise
                delay = self.policy.compute_delay(attempt)
                attempt += 1
                await asyncio.sleep(delay)

    def execute_with_retry(self, func: Callable[..., Any], *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """
        同步函数的重试包装。对于 coroutine 会转为 asyncio 运行。
        kwargs 可包含 context/phase/component，用于错误分类。
        """
        if asyncio.iscoroutinefunction(func):
            # 在同步上下文中运行 async 函数
            return asyncio.run(self.execute_with_retry_async(func, *args, timeout=timeout, **kwargs))

        start = time.time()
        attempt = 0
        last_exception = None
        while True:
            try:
                return func(*args, **kwargs)
            except Exception as exc:
                last_exception = exc
                err = ErrorClassifier.classify_error(exc, context=kwargs.get("context"), phase=kwargs.get("phase", "unknown"), component=kwargs.get("component", "unknown"))
                err.retry_count = attempt
                logger.error("RetryManager caught exception: %s | attempt=%d | error_id=%s", str(exc), attempt, err.error_id, extra={"error_record": err.to_dict()})
                if not self.should_retry(err):
                    logger.debug("Not retrying: %s", err.to_dict())
                    raise
                if timeout is not None and (time.time() - start) > timeout:
                    logger.debug("Retry timeout exceeded")
                    raise
                delay = self.policy.compute_delay(attempt)
                attempt += 1
                time.sleep(delay)


def execute_with_retry(func: Callable[..., Any], *args, retry_policy: Optional[RetryPolicy] = None, timeout: Optional[float] = None, **kwargs) -> Any:
    """
    便捷函数：使用 RetryManager 执行具有重试能力的调用。
    """
    manager = RetryManager(policy=retry_policy)
    return manager.execute_with_retry(func, *args, timeout=timeout, **kwargs)


def generate_recovery_suggestions(error_record: ErrorRecord) -> list[str]:
    """
    基础恢复建议生成：
    - 根据 error_type / phase / component 提供规则化建议
    - 结合上下文（如缺少文件路径、权限、网络）附加定制建议

    返回建议列表（按优先级从高到低排列）。
    """
    suggestions: list[str] = []

    error_type = (error_record.error_type or "").lower()
    phase = (error_record.phase or "").lower()
    component = (error_record.component or "").lower()
    ctx = error_record.context or {}

    # 通用规则表（可扩展）
    rules: Dict[tuple, list[str]] = {
        ("temporary", None, None): [
            "稍后重试操作（系统可能短暂不可用）",
            "检查网络连接与外部依赖服务状态",
        ],
        ("permanent", None, None): [
            "检查输入与配置是否正确",
            "验证文件/路径是否存在并可访问",
        ],
        ("user_error", None, None): [
            "校验用户输入参数与必需文件是否齐全",
            "根据错误提示修正请求后重试",
        ],
        ("system_error", None, None): [
            "检查磁盘/内存/句柄等系统资源是否充足",
            "查看系统与应用日志以定位根因",
        ],
        ("temporary", "document_processing", "document_processor"): [
            "确认 PRD 文档存在且可读",
            "如网络读取失败，请检查网络或稍后重试",
        ],
        ("permanent", "document_processing", "document_processor"): [
            "确认 docs/prd.md 文件路径与权限",
            "如文件缺失，先创建或修复后再执行",
        ],
    }

    # 匹配顺序：最具体(type+phase+component) -> type-only -> 默认
    def extend_rules(t: str, p: Optional[str], c: Optional[str]) -> None:
        if (t, p, c) in rules:
            suggestions.extend(rules[(t, p, c)])

    extend_rules(error_type, phase, component)
    extend_rules(error_type, None, None)

    # 上下文驱动建议
    path = ctx.get("path") or ctx.get("file_path") or ctx.get("src_path")
    if path:
        suggestions.append(f"检查文件是否存在且可读: {path}")

    if any(k in (error_record.error_message or "").lower() for k in ("permission", "denied")):
        suggestions.append("检查文件/目录权限，必要时以具有权限的用户运行")

    if any(k in (error_record.error_message or "").lower() for k in ("timeout", "connection", "broken pipe")):
        suggestions.append("确认网络可达并校验代理/防火墙设置")

    # 去重并保序
    seen: set[str] = set()
    unique_suggestions: list[str] = []
    for s in suggestions:
        if s not in seen:
            seen.add(s)
            unique_suggestions.append(s)

    return unique_suggestions