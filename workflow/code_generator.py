"""
代码生成引擎
实现基于 Story 和 Task 的代码生成功能
"""

import ast
import logging
import os
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import jinja2

from .models import CodeGenerationResult, FileSpec, TestGenerationResult, TestCase

logger = logging.getLogger(__name__)


class CodeGenerator:
    """核心代码生成引擎"""
    
    def __init__(self, project_root: str, template_dir: Optional[str] = None):
        """
        初始化代码生成器
        
        Args:
            project_root: 项目根目录路径
            template_dir: 模板目录路径，默认为 workflow/templates
        """
        self.project_root = Path(project_root)
        self.template_dir = Path(template_dir) if template_dir else self.project_root / "workflow" / "templates"
        
        # 初始化 Jinja2 环境
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(self.template_dir)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 简单模板缓存，避免重复从磁盘加载模板（keyed by template relative path）
        self._template_cache: Dict[str, jinja2.Template] = {}
        # 支持的编程语言
        self.supported_languages = {
            'python': {
                'extension': '.py',
                'template_dir': 'python',
                'formatter': 'black',
                'linter': 'flake8',
                'type_checker': 'mypy'
            },
            'javascript': {
                'extension': '.js',
                'template_dir': 'javascript',
                'formatter': None,
                'linter': None,
                'type_checker': None
            },
            'typescript': {
                'extension': '.ts',
                'template_dir': 'typescript',
                'formatter': None,
                'linter': None,
                'type_checker': None
            }
        }
        
        logger.info(f"CodeGenerator initialized with project_root: {self.project_root}")
    
    def generate_code(self, story: Dict[str, Any], task: Dict[str, Any]) -> CodeGenerationResult:
        """
        基于 Story 和 Task 生成代码
        
        Args:
            story: Story 数据
            task: Task 数据
            
        Returns:
            CodeGenerationResult: 代码生成结果
        """
        start_time = time.time()
        logger.info(f"Starting code generation for story: {story.get('id', 'unknown')}, task: {task.get('name', 'unknown')}")
        
        try:
            # 解析任务要求，生成文件规格
            file_specs = self._parse_task_requirements(story, task)
            
            # 生成代码文件
            generated_files = []
            modified_files = []
            deleted_files = []
            errors = []
            warnings = []
            
            for spec in file_specs:
                try:
                    if spec.operation == 'create':
                        result_files = self.create_files([spec])
                        generated_files.extend(result_files)
                    elif spec.operation == 'modify':
                        result_files = self.modify_files([spec])
                        modified_files.extend(result_files)
                    elif spec.operation == 'delete':
                        self._delete_file(spec.path)
                        deleted_files.append(spec.path)
                except Exception as e:
                    error_msg = f"Failed to {spec.operation} file {spec.path}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # 代码质量验证
            quality_metrics = {}
            validation_results = {}
            
            if generated_files or modified_files:
                all_files = generated_files + modified_files
                quality_metrics = self._analyze_code_quality(all_files)
                validation_results = self._validate_code(all_files)
            
            execution_time = time.time() - start_time
            success = len(errors) == 0
            
            result = CodeGenerationResult(
                success=success,
                generated_files=generated_files,
                modified_files=modified_files,
                deleted_files=deleted_files,
                operation_type=task.get('operation_type', 'create'),
                quality_metrics=quality_metrics,
                validation_results=validation_results,
                errors=errors,
                warnings=warnings,
                execution_time=execution_time
            )
            
            logger.info(f"Code generation completed. Success: {success}, Files: {len(generated_files + modified_files + deleted_files)}")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Code generation failed: {str(e)}"
            logger.error(error_msg)
            
            return CodeGenerationResult(
                success=False,
                generated_files=[],
                modified_files=[],
                deleted_files=[],
                operation_type=task.get('operation_type', 'create'),
                quality_metrics={},
                validation_results={},
                errors=[error_msg],
                warnings=[],
                execution_time=execution_time
            )
    
    def create_files(self, file_specs: List[FileSpec]) -> List[str]:
        """
        创建新文件
        
        Args:
            file_specs: 文件规格列表
            
        Returns:
            List[str]: 创建的文件路径列表
        """
        created_files = []
        
        for spec in file_specs:
            try:
                file_path = self.project_root / spec.path
                
                # 确保目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 生成文件内容
                if spec.template:
                    content = self._render_template(spec.template, spec.parameters, spec.language)
                else:
                    content = spec.content
                
                # 写入文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                created_files.append(spec.path)
                logger.info(f"Created file: {spec.path}")
                
            except Exception as e:
                logger.error(f"Failed to create file {spec.path}: {str(e)}")
                raise
        
        return created_files
    
    def modify_files(self, modifications: List[FileSpec]) -> List[str]:
        """
        修改现有文件
        
        Args:
            modifications: 修改规格列表
            
        Returns:
            List[str]: 修改的文件路径列表
        """
        modified_files = []
        
        for spec in modifications:
            try:
                file_path = self.project_root / spec.path
                
                if not file_path.exists():
                    logger.warning(f"File {spec.path} does not exist, creating new file")
                    self.create_files([spec])
                    modified_files.append(spec.path)
                    continue
                
                # 读取现有内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
                
                # 应用修改
                if spec.language == 'python':
                    new_content = self._modify_python_file(existing_content, spec)
                else:
                    # 对于非 Python 文件，直接替换内容
                    if spec.template:
                        new_content = self._render_template(spec.template, spec.parameters, spec.language)
                    else:
                        new_content = spec.content
                
                # 写入修改后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                modified_files.append(spec.path)
                logger.info(f"Modified file: {spec.path}")
                
            except Exception as e:
                logger.error(f"Failed to modify file {spec.path}: {str(e)}")
                raise
        
        return modified_files
    
    def _parse_task_requirements(self, story: Dict[str, Any], task: Dict[str, Any]) -> List[FileSpec]:
        """
        解析任务要求，生成文件规格
        
        Args:
            story: Story 数据
            task: Task 数据
            
        Returns:
            List[FileSpec]: 文件规格列表
        """
        file_specs = []
        
        # 基于任务描述推断需要创建的文件
        task_name = task.get('name', '')
        task_description = task.get('description', '')
        
        # 简单的规则匹配来确定文件类型和位置
        if 'CodeGenerator' in task_description or 'code_generator.py' in task_description:
            file_specs.append(FileSpec(
                path='workflow/code_generator.py',
                content='',
                language='python',
                template='python/class.py.j2',
                parameters={
                    'class_name': 'CodeGenerator',
                    'description': '代码生成引擎',
                    'methods': ['generate_code', 'create_files', 'modify_files']
                },
                operation='create'
            ))
        
        return file_specs
    
    def _render_template(self, template_name: str, parameters: Dict[str, Any], language: str) -> str:
        """
        渲染模板
        
        Args:
            template_name: 模板名称
            parameters: 模板参数
            language: 编程语言
            
        Returns:
            str: 渲染后的内容
        """
        try:
            # 构建完整的模板路径
            lang_config = self.supported_languages.get(language, {})
            template_dir = lang_config.get('template_dir', language)
            full_template_path = f"{template_dir}/{template_name}"
            
            # 使用缓存以减少磁盘 I/O 和模板解析开销
            template = self._template_cache.get(full_template_path)
            if template is None:
                template = self.jinja_env.get_template(full_template_path)
                # 将模板缓存在内存中
                self._template_cache[full_template_path] = template
            
            return template.render(**parameters)
            
        except Exception as e:
            logger.error(f"Failed to render template {template_name}: {str(e)}")
            raise
    
    def _modify_python_file(self, existing_content: str, spec: FileSpec) -> str:
        """
        使用 AST 修改 Python 文件
        
        Args:
            existing_content: 现有文件内容
            spec: 文件规格
            
        Returns:
            str: 修改后的内容
        """
        try:
            # 解析现有代码的 AST
            tree = ast.parse(existing_content)
            
            # 这里可以实现更复杂的 AST 操作
            # 目前简单返回新内容
            if spec.template:
                return self._render_template(spec.template, spec.parameters, spec.language)
            else:
                return spec.content
                
        except Exception as e:
            logger.error(f"Failed to modify Python file using AST: {str(e)}")
            # 回退到简单替换
            if spec.template:
                return self._render_template(spec.template, spec.parameters, spec.language)
            else:
                return spec.content
    
    def delete_files(self, file_paths: List[str]) -> List[str]:
        """
        删除多个文件

        Args:
            file_paths: 文件路径列表

        Returns:
            List[str]: 成功删除的文件路径列表
        """
        deleted_files = []

        for file_path in file_paths:
            try:
                self._delete_file(file_path)
                deleted_files.append(file_path)
            except Exception as e:
                logger.error(f"Failed to delete file {file_path}: {str(e)}")
                raise

        return deleted_files

    def rename_file(self, old_path: str, new_path: str) -> bool:
        """
        重命名文件

        Args:
            old_path: 原文件路径
            new_path: 新文件路径

        Returns:
            bool: 是否成功重命名
        """
        try:
            old_full_path = self.project_root / old_path
            new_full_path = self.project_root / new_path

            if not old_full_path.exists():
                logger.error(f"Source file {old_path} does not exist")
                return False

            # 确保目标目录存在
            new_full_path.parent.mkdir(parents=True, exist_ok=True)

            # 重命名文件
            old_full_path.rename(new_full_path)
            logger.info(f"Renamed file from {old_path} to {new_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to rename file from {old_path} to {new_path}: {str(e)}")
            return False

    def backup_files(self, file_paths: List[str], backup_dir: str = "backups") -> Dict[str, str]:
        """
        备份文件

        Args:
            file_paths: 要备份的文件路径列表
            backup_dir: 备份目录

        Returns:
            Dict[str, str]: 原文件路径到备份文件路径的映射
        """
        backup_mapping = {}
        backup_root = self.project_root / backup_dir
        backup_root.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for file_path in file_paths:
            try:
                source_path = self.project_root / file_path
                if not source_path.exists():
                    logger.warning(f"File {file_path} does not exist, skipping backup")
                    continue

                # 创建备份文件名
                backup_filename = f"{source_path.stem}_{timestamp}{source_path.suffix}"
                backup_path = backup_root / backup_filename

                # 复制文件
                import shutil
                shutil.copy2(source_path, backup_path)

                backup_mapping[file_path] = str(backup_path.relative_to(self.project_root))
                logger.info(f"Backed up {file_path} to {backup_mapping[file_path]}")

            except Exception as e:
                logger.error(f"Failed to backup file {file_path}: {str(e)}")
                raise

        return backup_mapping

    def rollback_files(self, backup_mapping: Dict[str, str]) -> List[str]:
        """
        回滚文件操作

        Args:
            backup_mapping: 备份映射（原文件路径 -> 备份文件路径）

        Returns:
            List[str]: 成功回滚的文件路径列表
        """
        rolled_back_files = []

        for original_path, backup_path in backup_mapping.items():
            try:
                source_path = self.project_root / backup_path
                target_path = self.project_root / original_path

                if not source_path.exists():
                    logger.error(f"Backup file {backup_path} does not exist")
                    continue

                # 确保目标目录存在
                target_path.parent.mkdir(parents=True, exist_ok=True)

                # 复制备份文件回原位置
                import shutil
                shutil.copy2(source_path, target_path)

                rolled_back_files.append(original_path)
                logger.info(f"Rolled back {original_path} from {backup_path}")

            except Exception as e:
                logger.error(f"Failed to rollback file {original_path}: {str(e)}")
                raise

        return rolled_back_files

    def _delete_file(self, file_path: str) -> None:
        """
        删除单个文件

        Args:
            file_path: 文件路径
        """
        try:
            full_path = self.project_root / file_path
            if full_path.exists():
                full_path.unlink()
                logger.info(f"Deleted file: {file_path}")
            else:
                logger.warning(f"File {file_path} does not exist")
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {str(e)}")
            raise
    
    def _analyze_code_quality(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        分析代码质量
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            Dict[str, Any]: 质量指标
        """
        metrics = {
            'total_files': len(file_paths),
            'python_files': 0,
            'total_lines': 0,
            'complexity_score': 0
        }
        
        for file_path in file_paths:
            full_path = self.project_root / file_path
            if full_path.suffix == '.py':
                metrics['python_files'] += 1
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        metrics['total_lines'] += len(content.splitlines())
                        
                        # 简单的复杂度分析
                        tree = ast.parse(content)
                        complexity = self._calculate_complexity(tree)
                        metrics['complexity_score'] += complexity
                        
                except Exception as e:
                    logger.warning(f"Failed to analyze file {file_path}: {str(e)}")
        
        return metrics
    
    def analyze_code_structure(self, file_path: str) -> Dict[str, Any]:
        """
        分析代码结构

        Args:
            file_path: 文件路径

        Returns:
            Dict[str, Any]: 代码结构分析结果
        """
        try:
            full_path = self.project_root / file_path
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)

            analysis = {
                'classes': [],
                'functions': [],
                'imports': [],
                'complexity': self._calculate_complexity(tree),
                'lines_of_code': len(content.splitlines()),
                'docstring_coverage': 0,
                'dependencies': []
            }

            # 分析类
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'line_number': node.lineno,
                        'methods': [],
                        'docstring': ast.get_docstring(node),
                        'complexity': self._calculate_complexity(node)
                    }

                    # 分析类方法
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                'name': item.name,
                                'line_number': item.lineno,
                                'args': [arg.arg for arg in item.args.args],
                                'docstring': ast.get_docstring(item),
                                'complexity': self._calculate_complexity(item)
                            }
                            class_info['methods'].append(method_info)

                    analysis['classes'].append(class_info)

                elif isinstance(node, ast.FunctionDef) and not any(isinstance(parent, ast.ClassDef) for parent in ast.walk(tree) if hasattr(parent, 'body') and node in getattr(parent, 'body', [])):
                    # 只处理顶级函数
                    function_info = {
                        'name': node.name,
                        'line_number': node.lineno,
                        'args': [arg.arg for arg in node.args.args],
                        'docstring': ast.get_docstring(node),
                        'complexity': self._calculate_complexity(node)
                    }
                    analysis['functions'].append(function_info)

                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis['imports'].append({
                                'module': alias.name,
                                'alias': alias.asname,
                                'type': 'import'
                            })
                    else:  # ImportFrom
                        for alias in node.names:
                            analysis['imports'].append({
                                'module': node.module,
                                'name': alias.name,
                                'alias': alias.asname,
                                'type': 'from_import'
                            })

            # 计算文档字符串覆盖率
            documented_items = 0
            total_items = len(analysis['classes']) + len(analysis['functions'])

            for cls in analysis['classes']:
                if cls['docstring']:
                    documented_items += 1
                for method in cls['methods']:
                    total_items += 1
                    if method['docstring']:
                        documented_items += 1

            for func in analysis['functions']:
                if func['docstring']:
                    documented_items += 1

            if total_items > 0:
                analysis['docstring_coverage'] = documented_items / total_items

            # 提取依赖关系
            analysis['dependencies'] = list(set([imp['module'] for imp in analysis['imports'] if imp['module']]))

            return analysis

        except Exception as e:
            logger.error(f"Failed to analyze code structure for {file_path}: {str(e)}")
            return {
                'error': str(e),
                'classes': [],
                'functions': [],
                'imports': [],
                'complexity': 0,
                'lines_of_code': 0,
                'docstring_coverage': 0,
                'dependencies': []
            }

    def analyze_code_dependencies(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        分析代码依赖关系

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 依赖关系分析结果
        """
        dependency_graph = {}
        all_dependencies = set()

        for file_path in file_paths:
            if file_path.endswith('.py'):
                analysis = self.analyze_code_structure(file_path)
                dependencies = analysis.get('dependencies', [])
                dependency_graph[file_path] = dependencies
                all_dependencies.update(dependencies)

        return {
            'dependency_graph': dependency_graph,
            'all_dependencies': list(all_dependencies),
            'internal_dependencies': [dep for dep in all_dependencies if not self._is_external_dependency(dep)],
            'external_dependencies': [dep for dep in all_dependencies if self._is_external_dependency(dep)]
        }

    def suggest_code_improvements(self, file_path: str) -> List[Dict[str, Any]]:
        """
        提供代码改进建议

        Args:
            file_path: 文件路径

        Returns:
            List[Dict[str, Any]]: 改进建议列表
        """
        suggestions = []
        analysis = self.analyze_code_structure(file_path)

        # 复杂度建议
        if analysis['complexity'] > 10:
            suggestions.append({
                'type': 'complexity',
                'severity': 'warning',
                'message': f"File has high complexity ({analysis['complexity']}). Consider refactoring.",
                'suggestion': 'Break down complex functions into smaller, more focused functions.'
            })

        # 文档字符串覆盖率建议
        if analysis['docstring_coverage'] < 0.8:
            suggestions.append({
                'type': 'documentation',
                'severity': 'info',
                'message': f"Low docstring coverage ({analysis['docstring_coverage']:.1%}). Consider adding more documentation.",
                'suggestion': 'Add docstrings to classes and functions to improve code maintainability.'
            })

        # 函数复杂度建议
        for func in analysis['functions']:
            if func['complexity'] > 5:
                suggestions.append({
                    'type': 'function_complexity',
                    'severity': 'warning',
                    'message': f"Function '{func['name']}' has high complexity ({func['complexity']}).",
                    'suggestion': f"Consider refactoring function '{func['name']}' to reduce complexity.",
                    'line_number': func['line_number']
                })

        # 类方法复杂度建议
        for cls in analysis['classes']:
            for method in cls['methods']:
                if method['complexity'] > 5:
                    suggestions.append({
                        'type': 'method_complexity',
                        'severity': 'warning',
                        'message': f"Method '{cls['name']}.{method['name']}' has high complexity ({method['complexity']}).",
                        'suggestion': f"Consider refactoring method '{method['name']}' in class '{cls['name']}'.",
                        'line_number': method['line_number']
                    })

        return suggestions

    def _is_external_dependency(self, module_name: str) -> bool:
        """
        判断是否为外部依赖

        Args:
            module_name: 模块名称

        Returns:
            bool: 是否为外部依赖
        """
        if not module_name:
            return False

        # Python 标准库模块
        stdlib_modules = {
            'os', 'sys', 'json', 'datetime', 'pathlib', 'typing', 'dataclasses',
            'asyncio', 'logging', 'subprocess', 'tempfile', 'shutil', 'ast',
            'time', 're', 'uuid', 'collections', 'itertools', 'functools'
        }

        # 检查是否为标准库模块
        root_module = module_name.split('.')[0]
        if root_module in stdlib_modules:
            return False

        # 检查是否为项目内部模块
        if module_name.startswith('workflow') or module_name.startswith('.'):
            return False

        return True

    def _calculate_complexity(self, tree: ast.AST) -> int:
        """
        计算代码复杂度（循环复杂度）

        Args:
            tree: AST 树

        Returns:
            int: 复杂度分数
        """
        complexity = 1  # 基础复杂度

        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.Try):
                complexity += 1
                # 每个 except 子句增加复杂度
                complexity += len(node.handlers)
            elif isinstance(node, (ast.With, ast.AsyncWith)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                # 布尔操作符（and, or）增加复杂度
                complexity += len(node.values) - 1

        return complexity
    
    def apply_code_formatting(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        应用代码格式化

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 格式化结果
        """
        results = {
            'success': True,
            'formatted_files': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 应用 black 格式化
            try:
                cmd = ['black'] + [str(self.project_root / f) for f in python_files]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    results['formatted_files'].extend(python_files)
                    logger.info(f"Successfully formatted {len(python_files)} Python files")
                else:
                    results['success'] = False
                    results['errors'].append(f"Black formatting failed: {result.stderr}")
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                results['success'] = False
                results['errors'].append(f"Black formatting failed: {str(e)}")

        return results

    def run_code_linting(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        运行代码 lint 检查

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: Lint 结果
        """
        results = {
            'success': True,
            'issues': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 运行 flake8
            try:
                cmd = ['flake8', '--max-line-length=88'] + [str(self.project_root / f) for f in python_files]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    logger.info(f"Flake8 check passed for {len(python_files)} files")
                else:
                    results['success'] = False
                    issues = result.stdout.strip().split('\n') if result.stdout.strip() else []
                    results['issues'].extend(issues)
                    results['warnings'].append(f"Flake8 found {len(issues)} issues")
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                results['errors'].append(f"Flake8 check failed: {str(e)}")

        return results

    def run_type_checking(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        运行类型检查

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 类型检查结果
        """
        results = {
            'success': True,
            'issues': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 运行 mypy
            try:
                cmd = ['mypy', '--ignore-missing-imports'] + [str(self.project_root / f) for f in python_files]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    logger.info(f"MyPy type check passed for {len(python_files)} files")
                else:
                    results['success'] = False
                    issues = result.stdout.strip().split('\n') if result.stdout.strip() else []
                    results['issues'].extend(issues)
                    results['warnings'].append(f"MyPy found {len(issues)} type issues")
            except (subprocess.TimeoutExpired, FileNotFoundError) as e:
                results['errors'].append(f"MyPy type check failed: {str(e)}")

        return results

    def validate_custom_rules(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        验证自定义编码规则

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 自定义规则验证结果
        """
        results = {
            'success': True,
            'violations': [],
            'errors': [],
            'warnings': []
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        for file_path in python_files:
            try:
                full_path = self.project_root / file_path
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # 检查禁止使用 print() 语句
                if 'print(' in content:
                    results['success'] = False
                    results['violations'].append(f"{file_path}: Found print() statement - use logging instead")

                # 检查是否有类型提示
                try:
                    tree = ast.parse(content)
                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            # 检查函数是否有返回类型提示
                            if node.returns is None and node.name != '__init__':
                                results['warnings'].append(f"{file_path}: Function '{node.name}' missing return type hint")

                            # 检查参数是否有类型提示
                            for arg in node.args.args:
                                if arg.annotation is None and arg.arg != 'self':
                                    results['warnings'].append(f"{file_path}: Parameter '{arg.arg}' in function '{node.name}' missing type hint")

                except SyntaxError:
                    # 语法错误会在其他地方处理
                    pass

            except Exception as e:
                results['errors'].append(f"Failed to validate custom rules for {file_path}: {str(e)}")

        return results

    def _validate_code(self, file_paths: List[str]) -> Dict[str, Any]:
        """
        验证代码质量（综合验证）

        Args:
            file_paths: 文件路径列表

        Returns:
            Dict[str, Any]: 验证结果
        """
        results = {
            'syntax_valid': True,
            'format_valid': True,
            'lint_valid': True,
            'type_valid': True,
            'custom_rules_valid': True,
            'errors': [],
            'warnings': [],
            'details': {}
        }

        python_files = [f for f in file_paths if f.endswith('.py')]

        if python_files:
            # 语法检查
            for file_path in python_files:
                full_path = self.project_root / file_path
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    ast.parse(content)
                except SyntaxError as e:
                    results['syntax_valid'] = False
                    results['errors'].append(f"Syntax error in {file_path}: {str(e)}")

            # 格式化检查
            format_results = self.apply_code_formatting(python_files)
            results['format_valid'] = format_results['success']
            results['details']['formatting'] = format_results
            if not format_results['success']:
                results['errors'].extend(format_results['errors'])

            # Lint 检查
            lint_results = self.run_code_linting(python_files)
            results['lint_valid'] = lint_results['success']
            results['details']['linting'] = lint_results
            if not lint_results['success']:
                results['warnings'].extend(lint_results['warnings'])

            # 类型检查
            type_results = self.run_type_checking(python_files)
            results['type_valid'] = type_results['success']
            results['details']['type_checking'] = type_results
            if not type_results['success']:
                results['warnings'].extend(type_results['warnings'])

            # 自定义规则检查
            custom_results = self.validate_custom_rules(python_files)
            results['custom_rules_valid'] = custom_results['success']
            results['details']['custom_rules'] = custom_results
            if not custom_results['success']:
                results['warnings'].extend(custom_results['warnings'])
                results['errors'].extend([v for v in custom_results['violations'] if 'print(' in v])

        return results


# Re-export TestGenerator for backward compatibility
try:
    from .test_generator import TestGenerator
except ImportError:
    # Fallback if test_generator module is not available
    TestGenerator = None


class TestExecutor:
    """测试执行引擎"""
    
    def __init__(self, project_root: str):
        """
        初始化测试执行器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root)
        logger.info(f"TestExecutor initialized with project_root: {self.project_root}")
    
    def execute_tests(self, test_files: List[str], pytest_args: List[str] = None) -> Dict[str, Any]:
        """
        执行测试文件
        
        Args:
            test_files: 测试文件路径列表
            pytest_args: 额外的 pytest 参数
            
        Returns:
            Dict[str, Any]: 测试执行结果
        """
        start_time = time.time()
        logger.info(f"Executing {len(test_files)} test files")
        
        results = {
            'success': False,
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'errors': [],
            'warnings': [],
            'execution_time': 0.0,
            'detailed_results': [],
            'coverage_info': {}
        }
        
        try:
            # 构建 pytest 命令
            test_paths = [str(self.project_root / tf) for tf in test_files]
            cmd = ['python3', '-m', 'pytest', '-v', '--tb=short']
            
            # 添加额外参数
            if pytest_args:
                cmd.extend(pytest_args)
            
            cmd.extend(test_paths)
            
            # 执行测试
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            execution_time = time.time() - start_time
            results['execution_time'] = execution_time
            
            # 解析结果
            stdout = result.stdout
            stderr = result.stderr
            
            # 分析输出
            results.update(self._parse_pytest_output(stdout, stderr))
            results['success'] = result.returncode == 0
            
            if results['success']:
                logger.info(f"All tests passed: {results['passed']} tests in {execution_time:.2f}s")
            else:
                logger.warning(f"Tests failed: {results['failed']} failed, {results['passed']} passed")
                if stderr:
                    results['errors'].append(f"Test execution errors: {stderr}")
            
        except subprocess.TimeoutExpired:
            results['errors'].append("Test execution timed out after 2 minutes")
            logger.error("Test execution timeout")
        except FileNotFoundError:
            results['errors'].append("pytest not found - please install pytest")
            logger.error("pytest not found")
        except Exception as e:
            results['errors'].append(f"Test execution failed: {str(e)}")
            logger.error(f"Test execution failed: {str(e)}")
        finally:
            results['execution_time'] = time.time() - start_time
        
        return results
    
    def execute_with_coverage(self, test_files: List[str], source_files: List[str]) -> Dict[str, Any]:
        """
        执行测试并生成覆盖率报告
        
        Args:
            test_files: 测试文件路径列表
            source_files: 源文件路径列表
            
        Returns:
            Dict[str, Any]: 测试执行结果包含覆盖率信息
        """
        logger.info(f"Executing tests with coverage for {len(source_files)} source files")
        
        # 先执行常规测试
        results = self.execute_tests(test_files, ['--cov=' + ','.join(source_files), '--cov-report=term'])
        
        # 生成详细覆盖率报告
        if results['success'] or results['passed'] > 0:
            coverage_results = self._generate_coverage_report(source_files)
            results['coverage_info'] = coverage_results
        
        return results
    
    def _parse_pytest_output(self, stdout: str, stderr: str) -> Dict[str, Any]:
        """
        解析 pytest 输出
        
        Args:
            stdout: 标准输出
            stderr: 错误输出
            
        Returns:
            Dict[str, Any]: 解析后的结果
        """
        parsed = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'skipped': 0,
            'detailed_results': []
        }
        
        try:
            lines = stdout.split('\n')
            for line in lines:
                # 解析测试结果行
                if ' PASSED' in line:
                    parsed['passed'] += 1
                    parsed['detailed_results'].append({
                        'test': line.split('::')[1] if '::' in line else line,
                        'status': 'PASSED'
                    })
                elif ' FAILED' in line:
                    parsed['failed'] += 1
                    parsed['detailed_results'].append({
                        'test': line.split('::')[1] if '::' in line else line,
                        'status': 'FAILED'
                    })
                elif ' SKIPPED' in line:
                    parsed['skipped'] += 1
                    parsed['detailed_results'].append({
                        'test': line.split('::')[1] if '::' in line else line,
                        'status': 'SKIPPED'
                    })
                # 解析总结行（例如：====== 2 passed, 1 failed in 1.23s ======）
                elif 'passed' in line and ('failed' in line or 'error' in line or 's =====' in line):
                    # 提取数字
                    import re
                    numbers = re.findall(r'(\d+)\s+(\w+)', line)
                    for num, status in numbers:
                        if status in ['passed', 'failed', 'skipped', 'error']:
                            parsed[status] = int(num)
            
            parsed['total_tests'] = parsed['passed'] + parsed['failed'] + parsed['skipped']
            
        except Exception as e:
            logger.warning(f"Failed to parse pytest output: {str(e)}")
        
        return parsed
    
    def _generate_coverage_report(self, source_files: List[str]) -> Dict[str, Any]:
        """
        生成覆盖率报告
        
        Args:
            source_files: 源文件路径列表
            
        Returns:
            Dict[str, Any]: 覆盖率报告
        """
        coverage_report = {
            'total_coverage': 0.0,
            'file_coverage': {},
            'uncovered_lines': {},
            'errors': []
        }
        
        try:
            # 生成覆盖率报告
            cmd = ['python3', '-m', 'coverage', 'report', '--show-missing']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                coverage_report.update(self._parse_coverage_output(result.stdout))
                logger.info(f"Coverage report generated: {coverage_report.get('total_coverage', 0):.1%}")
            else:
                coverage_report['errors'].append("Failed to generate coverage report")
                logger.error("Coverage report generation failed")
        
        except Exception as e:
            coverage_report['errors'].append(f"Coverage analysis failed: {str(e)}")
            logger.error(f"Coverage analysis failed: {str(e)}")
        
        return coverage_report
    
    def _parse_coverage_output(self, coverage_output: str) -> Dict[str, Any]:
        """
        解析覆盖率输出
        
        Args:
            coverage_output: coverage 命令输出
            
        Returns:
            Dict[str, Any]: 解析后的覆盖率数据
        """
        parsed_data = {
            'total_coverage': 0.0,
            'file_coverage': {},
            'uncovered_lines': {}
        }
        
        try:
            lines = coverage_output.strip().split('\n')
            for line in lines:
                if '%' in line and '---' not in line and 'Name' not in line:
                    parts = line.split()
                    if len(parts) >= 4:
                        if 'TOTAL' in line:
                            # 总覆盖率行
                            total_pct = parts[3].replace('%', '')
                            try:
                                parsed_data['total_coverage'] = float(total_pct) / 100.0
                            except ValueError:
                                pass
                        else:
                            # 单个文件覆盖率行
                            filename = parts[0]
                            coverage_pct = parts[3].replace('%', '')
                            try:
                                parsed_data['file_coverage'][filename] = float(coverage_pct) / 100.0
                            except ValueError:
                                continue
                            
                            # 提取未覆盖的行号（以文本形式保留，例如 "1-3,7"）
                            if len(parts) > 4:
                                missing = parts[4]
                                if missing and missing != '0':
                                    parsed_data['uncovered_lines'][filename] = missing

        except Exception as e:
            logger.warning(f"Failed to parse coverage output: {str(e)}")

        return parsed_data


    def _parse_coverage_xml(self, xml_content: str) -> Dict[str, Any]:
        """
        Parse coverage.py XML content and extract per-file uncovered line numbers.
        Returns dict with keys: total_coverage, file_coverage (float), uncovered_lines (list of ints)
        """
        import xml.etree.ElementTree as ET

        parsed = {
            'total_coverage': 0.0,
            'file_coverage': {},
            'uncovered_lines': {}
        }

        try:
            root = ET.fromstring(xml_content)
            total_lines = 0
            total_covered = 0

            # coverage.py XML structure: coverage/packages/package/classes/class/lines/line
            for class_elem in root.findall('.//class'):
                filename = class_elem.get('filename')
                if not filename:
                    continue
                lines_elem = class_elem.find('lines')
                if lines_elem is None:
                    continue
                file_uncovered = []
                valid = 0
                covered = 0
                for line in lines_elem.findall('line'):
                    valid += 1
                    hits = int(line.get('hits', '0') or 0)
                    number = int(line.get('number', '0') or 0)
                    if hits == 0:
                        file_uncovered.append(number)
                    else:
                        covered += 1
                if valid > 0:
                    parsed['file_coverage'][filename] = covered / valid
                    if file_uncovered:
                        parsed['uncovered_lines'][filename] = file_uncovered
                    total_lines += valid
                    total_covered += covered

            if total_lines > 0:
                parsed['total_coverage'] = total_covered / total_lines

        except Exception as e:
            logger.warning(f"Failed to parse coverage xml: {e}")

        return parsed


    def _suggest_tests_for_coverage_gaps(self, coverage_data: Dict[str, Any], max_suggestions_per_file: int = 3) -> Dict[str, Any]:
        """
        Given coverage_data with 'uncovered_lines', produce suggested TestCase-like dicts
        describing what tests to add. This is heuristic: it groups consecutive uncovered lines
        and suggests testing the function/class surrounding those lines where possible.

        Returns a dict:
          file_path -> list of suggestions (dicts with 'reason' and 'suggested_test' fields)
        """
        suggestions = {}

        for filename, uncovered in coverage_data.get('uncovered_lines', {}).items():
            try:
                full_path = self.project_root / filename
                if not full_path.exists():
                    # try relative path variants
                    alt = filename
                    if alt.startswith('./'):
                        alt = alt[2:]
                    full_path = self.project_root / alt
                if not full_path.exists():
                    suggestions[filename] = [{
                        'reason': 'file_missing',
                        'message': f"Source file {filename} not found in project root."
                    }]
                    continue

                source = full_path.read_text(encoding='utf-8')
                lines = source.splitlines()

                # Normalize uncovered representation: could be string like "1-3,7" or list of ints
                if isinstance(uncovered, str):
                    nums = []
                    for part in uncovered.split(','):
                        part = part.strip()
                        if '-' in part:
                            try:
                                a, b = part.split('-', 1)
                                nums.extend(range(int(a), int(b) + 1))
                            except Exception:
                                continue
                        else:
                            try:
                                nums.append(int(part))
                            except Exception:
                                continue
                    uncovered_lines = sorted(set(nums))
                else:
                    uncovered_lines = sorted(set(uncovered))

                # Group uncovered lines into ranges for concise suggestions
                ranges = []
                for ln in uncovered_lines:
                    if not ranges or ln > ranges[-1][1] + 1:
                        ranges.append([ln, ln])
                    else:
                        ranges[-1][1] = ln

                file_suggestions = []
                for r in ranges[:max_suggestions_per_file]:
                    start, end = r
                    context_snippet = "\n".join(lines[max(0, start-3): min(len(lines), end+2)])
                    # Heuristic: look backwards from start to find def or class
                    decl = None
                    for i in range(start-1, max(0, start-50), -1):
                        line_text = lines[i].strip()
                        if line_text.startswith('def ') or line_text.startswith('class '):
                            decl = line_text
                            break
                    reason = f"uncovered_lines_{start}_{end}"
                    suggested_test = {
                        'name': f"test_{Path(filename).stem}_covers_{start}_{end}",
                        'target': decl or f"{filename}:{start}-{end}",
                        'test_type': 'unit',
                        'reason': reason,
                        'suggestion': f"Add test targeting {decl or 'code region'} covering lines {start}-{end}. Context:\n{context_snippet}"
                    }
                    file_suggestions.append({
                        'reason': reason,
                        'suggested_test': suggested_test
                    })

                suggestions[filename] = file_suggestions

            except Exception as e:
                logger.warning(f"Failed to produce suggestions for {filename}: {e}")
                suggestions[filename] = [{
                    'reason': 'error',
                    'message': str(e)
                }]

        return suggestions
